/* Main container */
.container {
  min-height: 100vh;
  display: flex;
}

/* Overlay for loading and error states */
.overlay {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(20, 79, 137, 0.75);
}

/* Loading state */
.overlayContent {
  text-align: center;
}

.spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  height: 3rem;
  width: 3rem;
  border-bottom: 2px solid #2563eb;
  margin: 0 auto;
}

.loadingText {
  margin-top: 1rem;
  color: #4b5563;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Error state */
.errorContainer {
  text-align: center;
  max-width: 28rem;
  margin: 0 auto;
  padding: 1.5rem;
}

.errorIcon {
  color: #dc2626;
  font-size: 3.75rem;
  line-height: 1;
  margin-bottom: 1rem;
}

.errorTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
}

.errorMessage {
  color: #4b5563;
  margin-bottom: 1rem;
}

.retryButton {
  padding: 0.5rem 1rem;
  background-color: #2563eb;
  color: white;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retryButton:hover {
  background-color: #1d4ed8;
}

/* Main content area */
.mainContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
}

.mainContentCollapsed {
  margin-left: 4rem; /* ml-16 = 64px = 4rem */
}

.mainContentExpanded {
  margin-left: 16rem; /* ml-64 = 256px = 16rem */
}

/* Scrollable content */
.scrollableContent {
  flex: 1;
  overflow-y: auto;
  scroll-behavior: smooth;
  padding-bottom: 16rem; /* pb-64 = 256px = 16rem */
  padding-top: 4rem; /* pt-16 = 64px = 4rem */
}

.contentContainer {
  max-width: 80rem; /* max-w-7xl = 1280px = 80rem */
  margin: 0 auto;
  padding: 1.5rem 2rem; /* px-8 py-6 */
  display: flex;
  flex-direction: column;
  gap: 4rem; /* space-y-16 */
}

/* Fade in animation */
.fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
